// main_enhanced.cpp - 增强版银行账户管理系统主程序

#include "bankinterface.h"
#include <iostream>
#include <stdexcept>
#include <cstdlib>

#ifdef _WIN32
#include <windows.h> // Windows平台下设置控制台代码页需要
#endif

using namespace std;

int main() {
#ifdef _WIN32
    // 设置Windows控制台代码页为UTF-8，以正确显示中文字符
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    try {
        BankInterface interface;
        interface.run();
    } catch (const exception& e) {
        cout << "系统发生严重错误: " << e.what() << endl;
        cout << "程序将退出。" << endl;
        return 1;
    } catch (...) {
        cout << "系统发生未知错误，程序将退出。" << endl;
        return 1;
    }
    
    return 0;
}

