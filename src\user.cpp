#include "user.h"
#include "accountrecord.h"
#include "creditaccount.h"
#include "account.h"
#include <sstream>
#include <algorithm>
#include <memory>

User::User(const std::string& username, const std::string& password)
    : username(username), password(password) {
}

bool User::authenticate(const std::string& password) const {
    return this->password == password;
}

void User::addAccount(std::shared_ptr<Account> account) {
    accounts.push_back(account);
}

std::shared_ptr<Account> User::getAccount(size_t index) const {
    if (index < accounts.size()) {
        return accounts[index];
    }
    return nullptr;
}

double User::getTotalBalance() const {
    double total = 0.0;
    for (const auto& account : accounts) {
        total += account->getBalance();
    }
    return total;
}

double User::getMonthlyIncome(int year, int month) const {
    // 从账户记录中计算指定月份的收入（正金额交易）
    double income = 0.0;

    // 获取所有账户记录
    const auto& recordMap = Account::getRecordMap();

    for (const auto& record : recordMap) {
        const Date& date = record.first;
        const AccountRecord& accountRecord = record.second;

        // 检查是否是指定年月，且是当前用户的账户，且是收入（正金额）
        if (date.getYear() == year && date.getMonth() == month) {
            // 检查是否是当前用户的账户
            bool isUserAccount = false;
            for (const auto& account : accounts) {
                if (account->getId() == accountRecord.getAccount()) {
                    isUserAccount = true;
                    break;
                }
            }

            if (isUserAccount && accountRecord.getAmount() > 0) {
                income += accountRecord.getAmount();
            }
        }
    }

    return income;
}

double User::getMonthlyExpense(int year, int month) const {
    // 从账户记录中计算指定月份的支出（负金额交易）
    double expense = 0.0;

    // 获取所有账户记录
    const auto& recordMap = Account::getRecordMap();

    for (const auto& record : recordMap) {
        const Date& date = record.first;
        const AccountRecord& accountRecord = record.second;

        // 检查是否是指定年月，且是当前用户的账户，且是支出（负金额）
        if (date.getYear() == year && date.getMonth() == month) {
            // 检查是否是当前用户的账户
            bool isUserAccount = false;
            for (const auto& account : accounts) {
                if (account->getId() == accountRecord.getAccount()) {
                    isUserAccount = true;
                    break;
                }
            }

            if (isUserAccount && accountRecord.getAmount() < 0) {
                expense += -accountRecord.getAmount(); // 转为正数
            }
        }
    }

    return expense;
}

std::vector<std::string> User::getReminders() const {
    std::vector<std::string> reminders;

    for (const auto& account : accounts) {
        // 检查信用卡账户的还款提醒
        auto creditAccount = std::dynamic_pointer_cast<CreditAccount>(account);
        if (creditAccount && creditAccount->getBalance() < 0) {
            std::ostringstream oss;
            oss << "账户 " << account->getId() << " 需要还款: "
                << -creditAccount->getBalance() << " 元";
            reminders.push_back(oss.str());
        }

        // 检查余额过低提醒
        if (account->getBalance() < 100 && account->getBalance() >= 0) {
            std::ostringstream oss;
            oss << "账户 " << account->getId() << " 余额较低: "
                << account->getBalance() << " 元";
            reminders.push_back(oss.str());
        }
    }

    return reminders;
}

std::string User::serialize() const {
    std::ostringstream oss;
    oss << username << "|" << password << "|" << accounts.size();
    // 注意：这里简化了账户序列化，实际应用中需要更复杂的序列化
    return oss.str();
}

User User::deserialize(const std::string& data) {
    std::istringstream iss(data);
    std::string username, password;
    size_t accountCount;

    std::getline(iss, username, '|');
    std::getline(iss, password, '|');
    iss >> accountCount;

    User user(username, password);
    // 注意：这里简化了账户反序列化
    return user;
}
