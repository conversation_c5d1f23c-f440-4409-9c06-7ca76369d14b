#include "usermanager.h"
#include <fstream>
#include <iostream>
#include <algorithm>
#include <memory>
#include <cctype>

UserManager::UserManager(const std::string& dataFile) : dataFile(dataFile) {
    loadUsers();
}

UserManager::~UserManager() {
    saveUsers();
}

bool UserManager::registerUser(const std::string& username, const std::string& password) {
    if (!isValidUsername(username) || !isValidPassword(password)) {
        return false;
    }

    if (users.find(username) != users.end()) {
        return false; // 用户已存在
    }

    users[username] = std::make_shared<User>(username, password);
    saveUsers();
    return true;
}

bool UserManager::loginUser(const std::string& username, const std::string& password) {
    auto it = users.find(username);
    if (it != users.end() && it->second->authenticate(password)) {
        currentUser = it->second;
        return true;
    }
    return false;
}

void UserManager::logoutUser() {
    currentUser = nullptr;
}

void UserManager::saveUsers() {
    std::ofstream file(dataFile);
    if (file.is_open()) {
        for (const auto& pair : users) {
            file << pair.second->serialize() << std::endl;
        }
        file.close();
    }
}

void UserManager::loadUsers() {
    std::ifstream file(dataFile);
    if (file.is_open()) {
        std::string line;
        while (std::getline(file, line)) {
            if (!line.empty()) {
                try {
                    User user = User::deserialize(line);
                    users[user.getUsername()] = std::make_shared<User>(user);
                } catch (const std::exception& e) {
                    std::cerr << "Error loading user data: " << e.what() << std::endl;
                }
            }
        }
        file.close();
    }
}

bool UserManager::isValidUsername(const std::string& username) const {
    if (username.length() < 3 || username.length() > 20) {
        return false;
    }

    return std::all_of(username.begin(), username.end(), [](char c) {
        return std::isalnum(c) || c == '_';
    });
}

bool UserManager::isValidPassword(const std::string& password) const {
    return password.length() >= 6 && password.length() <= 50;
}
