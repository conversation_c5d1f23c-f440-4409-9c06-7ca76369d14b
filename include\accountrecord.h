#ifndef ACCOUNTRECORD_H
#define ACCOUNTRECORD_H
#include "date.h"
#include <string>
class AccountRecord {// 账目记录类
private:
    Date date;           // 日期
    std::string account; // 账户
    double amount;       // 金额
    double balance;      // 余额
    std::string desc;    // 描述
public:
    AccountRecord(const Date& date, const std::string& account, 
                 double amount, double balance, const std::string& desc);
    const Date& getDate() const { return date; }
    const std::string& getAccount() const { return account; }
    double getAmount() const { return amount; }
    double getBalance() const { return balance; }
    const std::string& getDesc() const { return desc; }
    void show() const; // 显示账目记录
};
#endif
