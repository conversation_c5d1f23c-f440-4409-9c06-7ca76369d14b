#ifndef BANKINTERFACE_H
#define BANKINTERFACE_H
#include "usermanager.h"
#include "date.h"
#include <vector>
#include <memory>
class BankInterface {
private:
    UserManager userManager;
    Date currentDate;
public:
    BankInterface();
    void run();  // 主界面
    void showLoginMenu();  // 登录/注册界面
    bool handleLogin();
    bool handleRegister();
    void showMainMenu();   // 主菜单
    void handleMainMenu();
    void createAccount();    // 账户操作
    void depositMoney();
    void withdrawMoney();
    void showAccounts();
    void showAccountStatistics();
    void queryRecords();   // 查询功能
    void queryRecordsByTime();
    void queryRecordsByAmount();
    void queryMonthlyRecords(); // 按月查询记录
    void changeDate(); // 日期操作
    void nextMonth();
    void showUserReminders();// 工具函数
    void showMonthlyStatistics();
    void clearScreen();
    void pauseScreen();
private:
    void handleException(const std::exception& e);
};
#endif
